package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;
import com.kedish.xyhelper_fox.constant.StatusEnum;
import com.kedish.xyhelper_fox.constant.UserTypeEnum;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.AddUserReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.RegisterReq;
import com.kedish.xyhelper_fox.repo.mapper.*;
import com.kedish.xyhelper_fox.repo.model.*;
import com.kedish.xyhelper_fox.utils.InvitationCodeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.kedish.xyhelper_fox.constant.Constant.REDIS_EMAIL_CODE_PREFIX;

@Service
@Slf4j
public class ChatgptUserService {

    private static final String SALT = "zjy_fox_1021";
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ChatGptUserMapper chatGptUserMapper;
    @Resource
    private InvitationRecordMapper invitationRecordMapper;
    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private GroupRateLimitMapper groupRateLimitMapper;

    @Resource
    private UserGroupMapper userGroupMapper;

    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;

    @Resource
    private UserGroupService userGroupService;

    @Resource
    private UserPointsRecordMapper userPointsRecordMapper;

    @Resource
    private LocalCache localCache;

    public static String hashPassword(String password) {
        try {

            int iterations = 10000;
            int keyLength = 256;
            byte[] salt = SALT.getBytes();
            PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), salt, iterations, keyLength);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            byte[] hash = keyFactory.generateSecret(spec).getEncoded();
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public ChatgptUser getChatGptUserByInviteCode(String inviteCode) {
        return chatGptUserMapper.selectOne(
                new QueryWrapper<ChatgptUser>()
                        .eq("invite_code", inviteCode)
        );
    }

    public Page<ChatgptUser> getPageUser(PageQueryReq req) {
        Page<ChatgptUser> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);

        }
        QueryWrapper<ChatgptUser> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.like("remark", req.getQuery())
                    .or()
                    .like("email", req.getQuery())
                    .or()
                    .like("userToken", req.getQuery());
        return chatGptUserMapper.selectPage(
                page, queryWrapper
        );
    }

    public ChatgptUser getUserByUserToken(String userToken) {
        return chatGptUserMapper.selectOne(
                new QueryWrapper<ChatgptUser>()
                        .eq("userToken", userToken)
        );
    }

    public UserGroup getUserGroup(String userToken) {
        ChatgptUser userByUserToken = getUserByUserToken(userToken);
        if (userByUserToken == null) {
            return null;
        }
        return userGroupMapper.selectById(userByUserToken.getGroupId());
    }

    public UserGroup getUserGroup(Long groupId) {

        return userGroupMapper.selectById(groupId);
    }

    public List<GroupRateLimit> getGroupRateLimit(String userToken) {
        UserGroup userGroup = getUserGroup(userToken);
        if (userGroup == null) {
            return null;
        }
        return groupRateLimitMapper.selectList(
                new QueryWrapper<GroupRateLimit>()
                        .eq("group_id", userGroup.getId())
        );
    }



    public ChatgptUser getUserByUserTokenOrEmail(String userToken, String email) {
        if (ObjectUtils.isEmpty(userToken) && ObjectUtils.isEmpty(email)) {
            return null;
        }

        QueryWrapper<ChatgptUser> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(userToken))
            queryWrapper.or(i -> i.eq("userToken", userToken));
        if (StringUtils.hasLength(email))
            queryWrapper.or(i -> i.eq("email", email));


        return chatGptUserMapper.selectOne(
                queryWrapper
        );
    }

    public void forgetPassword(RegisterReq req) {
        ChatgptUser userByUserTokenOrEmail = getUserByUserTokenOrEmail(req.getEmail(), req.getEmail());
        if (userByUserTokenOrEmail != null) {
            RBucket<Object> bucket = redissonClient.getBucket(REDIS_EMAIL_CODE_PREFIX + userByUserTokenOrEmail.getEmail());
            if (bucket.get() != null && bucket.get().equals(req.getCaptchaCode())) {
                userByUserTokenOrEmail.setPassword(hashPassword(req.getPassword()));
                chatGptUserMapper.updateById(userByUserTokenOrEmail);
                return;
            } else {
                throw new FoxException("user.email.code.invalid", null);
            }
        }
        throw new FoxException("user.not.found", null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void register(String userToken, String email, String code, String password, String inviteCode) {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("canRegister",
                "registerGiftPlus", "registerUseTime", "registerGiftTime", "register_gift_points",
                "useLimitPer", "useLimit"));

        if (!Boolean.parseBoolean(configMap.get("canRegister"))) {
            throw new FoxException("user.register.disabled", null);
        }

        RBucket<String> bucket = redissonClient.getBucket(REDIS_EMAIL_CODE_PREFIX + email);
        String codeInRedis = bucket.get();
        if (codeInRedis == null || !codeInRedis.equals(code)) {
            throw new FoxException("user.email.code.invalid", null);
        }
        ChatgptUser exist = getUserByUserTokenOrEmail(userToken, email);
        if (exist != null) {
            if (Objects.equals(exist.getUserToken(), userToken))
                throw new FoxException("user.token.exists", null);
            if (Objects.equals(exist.getEmail(), email))
                throw new FoxException("user.email.exists", null);
        }
        ChatgptUser chatgptUser = new ChatgptUser();
        chatgptUser.setEmail(email);
        chatgptUser.setUserToken(userToken);
        chatgptUser.setPassword(hashPassword(password));
        chatgptUser.setCreateTime(LocalDateTime.now());
        chatgptUser.setIsAdmin(false);
        chatgptUser.setUpdateTime(LocalDateTime.now());
        String registerGiftPlus = configMap.get("registerGiftPlus");
        chatgptUser.setExpireTime(LocalDateTime.now());
        chatgptUser.setPlusExpireTime(LocalDateTime.now());
        chatgptUser.setUserType(UserTypeEnum.COMMON.getCode());

        int registerUseTime = 0;

        if (StringUtils.hasLength(configMap.get("registerUseTime"))) {
            registerUseTime = Integer.parseInt(configMap.get("registerUseTime"));
        }

        if (registerUseTime == 0) {
            chatgptUser.setUserType(UserTypeEnum.COMMON.getCode());
        } else {
            if ("1".equals(registerGiftPlus)) {
                chatgptUser.setPlusExpireTime(LocalDateTime.now().plusHours(registerUseTime));
                chatgptUser.setExpireTime(LocalDateTime.now().plusHours(registerUseTime));
                chatgptUser.setUserType(UserTypeEnum.SVIP.getCode());
            } else {
                chatgptUser.setExpireTime(LocalDateTime.now().plusHours(registerUseTime));
                chatgptUser.setUserType(UserTypeEnum.VIP.getCode());
            }
        }


        chatgptUser.setIsPlus(false);
        chatgptUser.setStatus(StatusEnum.NORMAL.getCode());
        chatgptUser.setEnableClaude(false);
        chatgptUser.setInviteCode(InvitationCodeUtils.generateUniqueCode(userToken, userToken));
        chatgptUser.setLimit(0L);
        chatGptUserMapper.insert(chatgptUser);

        int registerGiftPoints = 0;
        if (StringUtils.hasLength(configMap.get("register_gift_points"))) {
            registerGiftPoints = Integer.parseInt(configMap.get("register_gift_points"));
        }
        if (registerGiftPoints > 0) {
            addUserLimit(userToken, registerGiftPoints, UserPointsRecord.SourceType.REGISTER_REWARD);
        }

        if (StringUtils.hasLength(inviteCode)) {
            ChatgptUser chatGptUserByInviteCode = getChatGptUserByInviteCode(inviteCode);

            if (chatGptUserByInviteCode != null) {
                chatgptUser.setInviteBy(chatGptUserByInviteCode.getUserToken());
                chatGptUserMapper.updateById(chatgptUser);


                InvitationRecord invitationRecord = new InvitationRecord();
                invitationRecord.setInviterName(chatGptUserByInviteCode.getUserToken());
                invitationRecord.setInviteeName(chatgptUser.getUserToken());
                invitationRecord.setInvitationTime(LocalDateTime.now());
                invitationRecordMapper.insert(invitationRecord);

                //邀请送时长
                //{"canRegister":true,"registerGiftPlus":true,"canLoginMulti":false,"registerUseTime":24,"registerGiftTime":24,"useLimitPer":"1h","useLimit":1,"loginExpire":24}
                try {
                    int giftTime = 0;
                    if (StringUtils.hasLength(configMap.get("registerGiftTime"))) {

                        giftTime = Integer.parseInt(configMap.get("registerGiftTime"));
                    }
                    if (giftTime > 0) {
                        if ("1".equals(registerGiftPlus)) {
                            chatgptUser.setPlusExpireTime(chatgptUser.getPlusExpireTime().plusHours(giftTime));
                            chatgptUser.setExpireTime(chatgptUser.getExpireTime().plusHours(giftTime));
                            chatGptUserByInviteCode.setPlusExpireTime(chatGptUserByInviteCode
                                    .getPlusExpireTime().plusHours(giftTime));
                        } else {
                            chatgptUser.setExpireTime(chatgptUser.getExpireTime().plusHours(giftTime));
                            chatGptUserByInviteCode.setExpireTime(chatGptUserByInviteCode
                                    .getExpireTime().plusHours(giftTime));
                        }
                    }
                    chatGptUserMapper.updateById(chatgptUser);
                } catch (Exception e) {
                    log.error("邀请送时长异常", e);
                }
            }
        }


    }

    public ChatgptUser login(String username, String password) {

        String hashedPassword = hashPassword(password);
        ChatgptUser chatgptUser = chatGptUserMapper
                .selectOne(
                        new QueryWrapper<ChatgptUser>()
                                .or(i -> i.and(j -> j.eq("userToken", username)
                                                .eq("password", hashedPassword))
                                        .or(j -> j.eq("email", username)
                                                .eq("password", hashedPassword)))
                );
        return chatgptUser;
    }

    public void addUserRights(Integer days, String membershipType, String userToken) {
        // 参数验证
        if (days == null || days <= 0) {
            throw new FoxException("invalid.days.parameter", null);
        }
        if (!StringUtils.hasLength(membershipType)) {
            throw new FoxException("invalid.membership.type", null);
        }
        if (!StringUtils.hasLength(userToken)) {
            throw new FoxException("invalid.user.token", null);
        }

        ChatgptUser user = getUserByUserToken(userToken);
        if (user == null) {
            throw new FoxException("user.not.found", null);
        }

        LocalDateTime now = LocalDateTime.now();

        // 修正逻辑：general对应expireTime，plus对应plusExpireTime
        if (membershipType.equals("general")) {
            LocalDateTime expireTime = user.getExpireTime();
            LocalDateTime toCalc = expireTime.isAfter(now) ? expireTime : now;
            user.setExpireTime(toCalc.plusDays(days));
        } else if (membershipType.equals("plus")) {
            LocalDateTime plusExpireTime = user.getPlusExpireTime();
            LocalDateTime toCalc = plusExpireTime.isAfter(now) ? plusExpireTime : now;
            user.setPlusExpireTime(toCalc.plusDays(days));
        } else {
            throw new FoxException("unsupported.membership.type", null);
        }

        user.setUpdateTime(LocalDateTime.now());

        // 检查用户组是否存在
        UserGroup userGroup = userGroupService.getByName(membershipType);
        if (userGroup == null) {
            throw new FoxException("user.group.not.found", null);
        }
        user.setGroupId(userGroup.getId());

        chatGptUserMapper.updateById(user);
        userLimitBucketComponent.resetBucket(user);
    }

    public void updatePassword(String username, String oldPassword, String newPassword) {
        ChatgptUser chatgptUser = login(username, oldPassword);

        if (chatgptUser == null) {
            throw new FoxException("user.old.password.invalid", null);
        }
        chatgptUser.setPassword(hashPassword(newPassword));
        chatGptUserMapper.updateById(chatgptUser);
    }

    public void deleteUser(Long id) {
        chatGptUserMapper.deleteById(id);
    }

    public void addOrUpdateUser(AddUserReq req) {

        if (req.getId() != null) {
            ChatgptUser chatgptUser = chatGptUserMapper.selectById(req.getId());
            String prePer = chatgptUser.getPer();
            Long preLimit = chatgptUser.getLimit();
//            chatgptUser.setUserToken(req.getUsername());
            chatgptUser.setPassword(hashPassword(req.getPassword()));
            chatgptUser.setEmail(req.getEmail());
            chatgptUser.setRemark(req.getRemark());
            chatgptUser.setUserType(req.getUserType());
            chatgptUser.setExpireTime(req.getExpireTime());
            chatgptUser.setPlusExpireTime(req.getPlusExpireTime());
            chatgptUser.setEnableClaude(req.getEnableClaude());
            chatgptUser.setLimit(req.getLimit());
            chatgptUser.setPer(req.getPer());
            chatgptUser.setStatus(req.getStatus());

            if (!Objects.equals(req.getGroupId(),chatgptUser.getGroupId())) {
                chatgptUser.setGroupId(req.getGroupId());
                userLimitBucketComponent.resetBucket(chatgptUser);
            }
            chatGptUserMapper.updateById(chatgptUser);

        } else {

            String userToken = req.getUsername();
            String email = req.getEmail();
            ChatgptUser exist = getUserByUserTokenOrEmail(userToken, email);
            if (exist != null) {
                if (Objects.equals(exist.getUserToken(), userToken))
                    throw new FoxException("user.token.exists", null);
                if (Objects.equals(exist.getEmail(), email))
                    throw new FoxException("user.email.exists", null);
            }

            ChatgptUser chatgptUser = new ChatgptUser();
            chatgptUser.setUserToken(req.getUsername());
            chatgptUser.setPassword(hashPassword(req.getPassword()));
            chatgptUser.setLimit(req.getLimit());
            chatgptUser.setPer(req.getPer());
            if (StringUtils.hasLength(req.getEmail())) {

                chatgptUser.setEmail(req.getEmail());
            }
            chatgptUser.setRemark(req.getRemark());
            chatgptUser.setUserType(req.getUserType());
            chatgptUser.setExpireTime(req.getExpireTime());
            chatgptUser.setPlusExpireTime(req.getPlusExpireTime());
            chatgptUser.setStatus(StatusEnum.NORMAL.getCode());
            chatgptUser.setEnableClaude(req.getEnableClaude());
            chatgptUser.setCreateTime(LocalDateTime.now());
            chatgptUser.setUpdateTime(LocalDateTime.now());

            Integer register_gift_points = 0;
            try {
                register_gift_points = Integer.parseInt(localCache.getConfigMap().getOrDefault("register_gift_points", "0"));
            } catch (Exception e) {
                log.error("register_gift_points配置错误", e);
            }
            chatgptUser.setLimit(0L);
            chatGptUserMapper.insert(chatgptUser);

            addUserLimit(chatgptUser.getUserToken(), register_gift_points, UserPointsRecord.SourceType.REGISTER_REWARD);

        }
    }

    public java.util.List<ChatgptUser> getUsersByGroupId(Long groupId) {
        return chatGptUserMapper.selectByGroupId(groupId);
    }

    /**
     * Get all users with groupId not equal to the specified groupId
     *
     * @param groupId The groupId to exclude
     * @return List of users with groupId not equal to the specified groupId
     */
    public List<ChatgptUser> getUsersByGroupIdNotEqual(Long groupId) {
        QueryWrapper<ChatgptUser> queryWrapper = new QueryWrapper<ChatgptUser>()
                .ne("group_id", groupId);
        return chatGptUserMapper.selectList(queryWrapper);
    }

    /**
     * Scheduled task that runs every 30 minutes to check users with groupId not equal to 1.
     * If both expireTime and plusExpireTime are expired, sets the user's groupId to 1.
     */
    @Scheduled(fixedRate = 30, timeUnit = TimeUnit.MINUTES)
    public int checkAndUpdateExpiredUsers() {
        log.info("Starting scheduled task to check and update expired users");

        // Query users with groupId not equal to 1
        QueryWrapper<ChatgptUser> queryWrapper = new QueryWrapper<ChatgptUser>()
                .ne("group_id", 1);
        List<ChatgptUser> users = chatGptUserMapper.selectList(queryWrapper);

        log.info("Found {} users with groupId not equal to 1", users.size());

        LocalDateTime now = LocalDateTime.now();
        int updatedCount = 0;

        for (ChatgptUser user : users) {
            // Check if both expireTime and plusExpireTime are in the past
            if (user.getExpireTime().isBefore(now) && user.getPlusExpireTime().isBefore(now)) {
                // Update user's groupId to 1
                user.setGroupId(1L);
                chatGptUserMapper.updateById(user);

                // Reset user's bucket if needed
                userLimitBucketComponent.resetBucket(user);

                updatedCount++;
                log.info("Updated user {} (ID: {}) groupId to 1 due to expired times", user.getUserToken(), user.getId());
            }
        }

        log.info("Completed scheduled task. Updated {} users to groupId 1", updatedCount);
        return updatedCount;
    }

    public void checkUserLimitEnough(String userToken, Integer limit) {
        ChatgptUser user = getUserByUserToken(userToken);
        if (user == null) {
            throw new FoxException("user.not.found", null);
        }
        if (user.getLimit() == null) {
            throw new FoxException(FoxException.NOT_ENOUGH_POINTS, "user.points.insufficient");
        }
        if (user.getLimit() < limit) {
            throw new FoxException(FoxException.NOT_ENOUGH_POINTS, "user.points.insufficient");
        }
    }

    public void addUserLimit(String userToken, Integer limit, UserPointsRecord.SourceType sourceType) {
        ChatgptUser user = getUserByUserToken(userToken);
        if (user == null) {
            throw new FoxException("user.not.found", null);
        }
        user.setLimit(user.getLimit() + limit);
        user.setUpdateTime(LocalDateTime.now());
        chatGptUserMapper.updateById(user);

        UserPointsRecord userPointsRecord = new UserPointsRecord();
        userPointsRecord.setUserToken(userToken);
        userPointsRecord.setPointsAmount(limit);
        userPointsRecord.setRecordType(limit > 0 ? UserPointsRecord.RecordType.EARN.getValue() : UserPointsRecord.RecordType.CONSUME.getValue());
        userPointsRecord.setSourceType(sourceType.getValue());
        userPointsRecord.setDeleted(false);
//        userPointsRecord.setDescription(sourceType.getValue())
        userPointsRecord.setBalanceAfter(Math.toIntExact(user.getLimit()));
        userPointsRecord.setCreatedAt(LocalDateTime.now());
        userPointsRecord.setUpdatedAt(LocalDateTime.now());
        userPointsRecordMapper.insert(userPointsRecord);
        log.info("用户{}获得了{}个积分", userToken, limit);
    }
}
